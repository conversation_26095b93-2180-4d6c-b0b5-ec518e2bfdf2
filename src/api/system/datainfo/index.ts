import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 数据信息信息 */
export interface DataInfo {
          id: number; // 编号
          type?: number; // 类型
          value?: string; // 结果
          batchId: number; // 批次ID
          userId: number; // 用户ID
  }

// 数据信息 API
export const DataInfoApi = {
  // 查询数据信息分页
  getDataInfoPage: async (params: any) => {
    return await request.get({ url: `/system/data-info/page`, params })
  },

  // 查询数据信息详情
  getDataInfo: async (id: number) => {
    return await request.get({ url: `/system/data-info/get?id=` + id })
  },

  // 新增数据信息
  createDataInfo: async (data: DataInfo) => {
    return await request.post({ url: `/system/data-info/create`, data })
  },

  // 修改数据信息
  updateDataInfo: async (data: DataInfo) => {
    return await request.put({ url: `/system/data-info/update`, data })
  },

  // 删除数据信息
  deleteDataInfo: async (id: number) => {
    return await request.delete({ url: `/system/data-info/delete?id=` + id })
  },

  /** 批量删除数据信息 */
  deleteDataInfoList: async (ids: number[]) => {
    return await request.delete({ url: `/system/data-info/delete-list?ids=${ids.join(',')}` })
  },

  // 导出数据信息 Excel
  exportDataInfo: async (params) => {
    return await request.download({ url: `/system/data-info/export-excel`, params })
  },
}

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="查询结果" prop="value">
        <el-input
          v-model="queryParams.value"
          placeholder="请输入要查询的结果（精确匹配）"
          clearable
          @keyup.enter="handleQuery"
          class="!w-400px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          查询
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 查询结果 -->
  <ContentWrap>
    <div v-if="!hasSearched" class="text-center text-gray-500 py-20">
      <Icon icon="ep:search" size="48" class="mb-4" />
      <p class="text-lg">请输入要查询的结果进行搜索</p>
      <p class="text-sm">支持精确匹配查询</p>
    </div>

    <div v-else-if="loading" class="text-center py-20">
      <el-loading-spinner size="large" />
      <p class="mt-4 text-gray-500">正在查询中...</p>
    </div>
    
    <div v-else-if="list.length === 0" class="text-center text-gray-500 py-20">
      <Icon icon="ep:document-remove" size="48" class="mb-4" />
      <p class="text-lg">未找到匹配的结果</p>
      <p class="text-sm">请尝试其他查询条件</p>
    </div>
    
    <el-table
      v-else
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      class="w-full"
    >
      <el-table-column label="编号" align="center" prop="id" width="80" />
      <el-table-column label="类型" align="center" prop="type" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_DATA_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="查询结果" align="center" prop="value" min-width="200">
        <template #default="scope">
          <el-text class="font-medium text-primary">{{ scope.row.value }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="批次ID" align="center" prop="batchId" width="100" />
      <el-table-column label="用户ID" align="center" prop="userId" width="100" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
    </el-table>
    
    <!-- 查询统计信息 -->
    <div v-if="list.length > 0" class="mt-4 text-sm text-gray-600">
      <el-alert
        :title="`共找到 ${list.length} 条匹配记录`"
        type="success"
        :closable="false"
        show-icon
      />
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { DataInfoApi, DataInfo } from '@/api/system/datainfo'

/** 游客数据信息查询页面 */
defineOptions({ name: 'DataInfoGuest' })

const message = useMessage() // 消息弹窗

const loading = ref(false) // 查询的加载中
const hasSearched = ref(false) // 是否已经进行过搜索
const list = ref<DataInfo[]>([]) // 查询结果列表
const queryParams = reactive({
  value: ''
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  if (!queryParams.value || queryParams.value.trim() === '') {
    message.warning('请输入要查询的结果')
    return
  }
  
  loading.value = true
  hasSearched.value = true
  try {
    const data = await DataInfoApi.getDataInfoByValueForGuest(queryParams.value.trim())
    list.value = data || []
  } catch (error) {
    console.error('查询失败:', error)
    message.error('查询失败，请稍后重试')
    list.value = []
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value = ''
  list.value = []
  hasSearched.value = false
}

// 页面加载时检查URL参数
onMounted(() => {
  const route = useRoute()
  if (route.query.value) {
    queryParams.value = route.query.value as string
    handleQuery()
  }
})
</script>

<style scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
